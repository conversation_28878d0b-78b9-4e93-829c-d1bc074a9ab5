"use client";
import React, { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";

export default function RouteGuard({
  children,
  role,
}: {
  children: React.ReactNode;
  role?: "CLIENT" | "FREELANCER";
}) {
  const { loading, user, isAuthenticated } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Skip if still loading
    if (loading) return;

    // If user is not authenticated, redirect to login
    if (!isAuthenticated || !user) {
      // Store the current path to redirect back after login
      if (pathname !== "/login") {
        sessionStorage.setItem("redirectAfterLogin", pathname);
      }
      router.replace("/login");
      return;
    }

    // Get user role from attributes
    const userRole = user.attributes?.["custom:role"] as
      | "CLIENT"
      | "FREELANCER"
      | undefined;

    // If route requires a specific role and user doesn't have it
    if (role && userRole !== role) {
      // Redirect to appropriate dashboard based on user role
      const redirectPath =
        userRole === "CLIENT"
          ? "/client/dashboard"
          : userRole === "FREELANCER"
          ? "/freelancer/dashboard"
          : "/";

      // Only redirect if not already on the correct path to prevent loops
      if (pathname !== redirectPath) {
        router.replace(redirectPath);
        return;
      }
    }

    // User is authorized - no state update needed as we handle everything in the effect
  }, [loading, user, isAuthenticated, role, router, pathname]);

  // Show loading state while checking auth status
  if (loading || !isAuthenticated || !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        Loading...
      </div>
    );
  }

  // Get user role for final check
  const userRole = user.attributes?.["custom:role"] as
    | "CLIENT"
    | "FREELANCER"
    | undefined;

  // Final role check (in case the above effect hasn't run yet)
  if (role && userRole !== role) {
    return null;
  }

  return <>{children}</>;
}
