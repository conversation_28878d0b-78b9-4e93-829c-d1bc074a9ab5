'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/AuthContext';
import { Button } from '@/components/ui/Button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Container } from './Container';
import { cn } from '@/lib/utils';
import { Dropdown } from '@/components/ui/Dropdown';
import { LogOut, User, Settings } from 'lucide-react';

export interface NavItem {
  name: string;
  href: string;
  subItems?: NavItem[];
}

export interface ModernNavbarProps {
  navigation?: NavItem[];
  className?: string;
}

const ModernNavbar: React.FC<ModernNavbarProps> = ({ navigation = [], className }) => {
  const { isAuthenticated, user, signOut } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  const userRole = user?.attributes?.['custom:role'] as string || 'CLIENT';

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const getUserInitials = () => {
    const name = user?.attributes?.name || user?.username || '';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <nav className={cn('sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60', className)}>
      <Container>
        <div className="flex h-16 items-center justify-between">
          {/* Logo - Pushed to the left */}
          <div className="flex-1">
            <Link href="/" className="flex items-center space-x-2 w-fit">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">MV</span>
              </div>
              <span className="text-xl font-bold text-foreground">MyVillage</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
              >
                {item.name}
              </Link>
            ))}
          </div>

          {/* User Menu - Pushed to the right */}
          <div className="flex items-center space-x-2">
            {isAuthenticated ? (
              <div className="flex items-center">
                <div className="hidden md:flex flex-col items-end mr-2">
                  <p className="text-sm font-medium text-foreground">
                    {user?.attributes?.name || user?.username || 'User'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {userRole.charAt(0).toUpperCase() + userRole.slice(1).toLowerCase()}
                  </p>
                </div>
                <Dropdown
                  trigger={
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full p-0 hover:bg-accent">
                      <Avatar className="h-9 w-9 border">
                        {typeof user?.attributes?.picture === 'string' && user.attributes.picture && (
                          <AvatarImage src={user.attributes.picture} alt={`${user?.attributes?.name || user?.username || 'User'} avatar`} />
                        )}
                        <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  }
                items={[
                  {
                    id: 'user-info',
                    label: (
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user?.attributes?.name || user?.username}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user?.attributes?.email}
                        </p>
                        <div className="mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {userRole}
                          </Badge>
                        </div>
                      </div>
                    ),
                    disabled: true
                  },
                  { id: 'separator-1', label: '', separator: true },
                  {
                    id: 'profile',
                    label: 'Profile',
                    icon: <User className="h-4 w-4 mr-2" />,
                    onClick: () => window.location.href = '/(protected)/profile'
                  },
                  {
                    id: 'settings',
                    label: 'Settings',
                    icon: <Settings className="h-4 w-4 mr-2" />,
                    onClick: () => window.location.href = '/(protected)/settings'
                  },
                  { id: 'separator-2', label: '', separator: true },
                  {
                    id: 'logout',
                    label: 'Log out',
                    icon: <LogOut className="h-4 w-4 mr-2" />,
                    onClick: handleSignOut
                  }
                ]}
                align="right"
                className="ml-2"
                menuClassName="w-56"
              />
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/login">Sign in</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/signup">Sign up</Link>
                </Button>
              </div>
            )}

            {/* Mobile menu button */}
            <button
              type="button"
              className="md:hidden inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </Container>
    </nav>
  );
};

export { ModernNavbar };
