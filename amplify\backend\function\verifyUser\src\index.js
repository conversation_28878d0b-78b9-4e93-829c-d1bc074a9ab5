
const { CognitoIdentityProviderClient, AdminSetUserPasswordCommand, AdminGetUserCommand } = require('@aws-sdk/client-cognito-identity-provider');
const client = new CognitoIdentityProviderClient({ region: process.env.AWS_REGION || 'us-east-1' });

exports.handler = async (event) => {

  const userPoolId = process.env.USER_POOL_ID;
  let username, password;
  // Support API Gateway event structure
  if (event.body) {
    try {
      const body = JSON.parse(event.body);
      username = body.username;
      password = body.password; // required for setting password
    } catch (e) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Invalid request body' })
      };
    }
  } else {
    username = event.username;
    password = event.password;
  }

  if (!username || !password) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'username and password are required' })
    };
  }

  try {
    // Set permanent password and confirm user
    const setPwdCmd = new AdminSetUserPasswordCommand({
      UserPoolId: userPoolId,
      Username: username,
      Password: password,
      Permanent: true
    });
    await client.send(setPwdCmd);

    // Verify user status
    const getUserCmd = new AdminGetUserCommand({
      UserPoolId: userPoolId,
      Username: username
    });
    const result = await client.send(getUserCmd);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'User confirmed successfully', userStatus: result.UserStatus })
    };
  } catch (err) {
    console.error("Error confirming user:", err);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: err.message || JSON.stringify(err) })
    };
  }
};
