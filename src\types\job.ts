export type JobCategory = 
  | 'WEB_DEVELOPMENT'
  | 'MO<PERSON>LE_DEVELOPMENT'
  | 'DESIGN'
  | 'WRITING'
  | 'MARKETING'
  | 'BUSINESS'
  | 'OTHER';

export type JobStatus = 
  | 'OPEN'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';

export interface Job {
  id: string;
  title: string;
  description: string;
  budget: number;
  category: JobCategory;
  deadline?: string; // ISO date string
  clientId: string;
  clientName?: string;
  status?: JobStatus;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
  createdAt?: string; // ISO date string
  updatedAt?: string; // ISO date string
}

export interface CreateJobInput {
  title: string;
  description: string;
  budget: number;
  category: JobCategory;
  deadline: string; // ISO date string
}

export interface UpdateJobInput extends Partial<Omit<CreateJobInput, 'clientId'>> {
  id: string;
}

export interface JobFilter {
  category?: JobCategory;
  minBudget?: number;
  maxBudget?: number;
  search?: string;  // For general text search
  clientId?: string; // For filtering jobs by client
  status?: JobStatus;
  isRemote?: boolean;
  skills?: string[];
  searchTerm?: string; // For backward compatibility
}

export type JobApplicationStatus = 'PENDING' | 'ACCEPTED' | 'REJECTED';

export interface JobApplication {
  id: string;
  jobId: string;
  freelancerId: string;
  freelancerName?: string;
  coverLetter?: string;
  bidAmount: number;
  proposedRate?: number; // Added proposedRate as an optional property
  status: JobApplicationStatus;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateJobApplicationInput {
  jobId: string;
  coverLetter?: string;
  bidAmount: number;
}

export interface JobWithApplications extends Job {
  applications: JobApplication[];
}
