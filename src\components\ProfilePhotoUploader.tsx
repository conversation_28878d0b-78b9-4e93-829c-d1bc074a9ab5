"use client";
import React, { useEffect, useState } from "react";
import { useAuth } from "@/lib/auth/AuthContext";

export default function ProfilePhotoUploader() {
  const { user, updateProfile } = useAuth();
  const [url, setUrl] = useState<string | null>(user?.profilePhoto ?? null);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    setUrl(user?.profilePhoto ?? null);
  }, [user?.profilePhoto]);

  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setUploading(true);
    try {
      // MVP: use a temporary object URL (non-persistent). Replace with Amplify Storage when enabled.
      const objectUrl = URL.createObjectURL(file);
      setUrl(objectUrl);
      await updateProfile({ profilePhoto: objectUrl });
    } catch (e) {
      console.error(e);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="flex items-center gap-4">
      {url ? (
        // eslint-disable-next-line @next/next/no-img-element
        <img src={url} alt="profile" className="w-16 h-16 rounded-full object-cover border" />
      ) : (
        <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-xs">No photo</div>
      )}
      <label className="text-sm">
        <span className="bg-gray-100 border px-3 py-1 rounded cursor-pointer">{uploading ? "Uploading..." : "Upload"}</span>
        <input className="hidden" type="file" accept="image/*" onChange={onFileChange} disabled={uploading} />
      </label>
    </div>
  );
}

