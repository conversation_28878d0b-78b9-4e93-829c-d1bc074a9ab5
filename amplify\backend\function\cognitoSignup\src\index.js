const {
  CognitoIdentityProviderClient,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  AdminAddUserToGroupCommand,
} = require("@aws-sdk/client-cognito-identity-provider");

const client = new CognitoIdentityProviderClient({ region: process.env.REGION });

exports.handler = async (event) => {
  try {
    const body = JSON.parse(event.body);
    const { email, password, role } = body;

    if (!email || !password || !role) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: "Missing required fields: email, password, role" }),
      };
    }

    const userPoolId = process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLID;
    const groupName = role || process.env.DEFAULT_USER_GROUP;

    // 1. Create the user
    await client.send(
      new AdminCreateUserCommand({
        UserPoolId: userPoolId,
        Username: email,
        UserAttributes: [
          { Name: "email", Value: email },
          { Name: "email_verified", Value: "true" },
        ],
        MessageAction: "SUPPRESS", // don't send welcome email
      })
    );

    // 2. Set the password
    await client.send(
      new AdminSetUserPasswordCommand({
        UserPoolId: userPoolId,
        Username: email,
        Password: password,
        Permanent: true,
      })
    );

    // 3. Add user to the group
    await client.send(
      new AdminAddUserToGroupCommand({
        UserPoolId: userPoolId,
        Username: email,
        GroupName: groupName,
      })
    );

    return {
      statusCode: 201,
      body: JSON.stringify({ message: "User signed up and added to group successfully" }),
    };
  } catch (err) {
    console.error("Signup error:", err);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Signup failed", error: err.message }),
    };
  }
};
