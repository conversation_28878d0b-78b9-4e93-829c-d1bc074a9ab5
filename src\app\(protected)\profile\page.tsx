"use client";
import React, { useEffect, useState } from "react";
import RouteGuard from "../../../components/RouteGuard";
import ProfilePhotoUploader from "../../../components/ProfilePhotoUploader";
import { useAuth } from "@/lib/auth/AuthContext";

export default function ProfilePage() {
  const { user, updateProfile, cognitoUserId, refresh } = useAuth();
  const [name, setName] = useState("");
  const [bio, setBio] = useState("");
  const [skills, setSkills] = useState("");
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      setName(user.name || "");
      setBio(user.bio || "");
      setSkills((user.skills || []).join(", "));
    }
  }, [user]);

  const onSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      await updateProfile({ name, bio, skills: skills.split(",").map((s) => s.trim()).filter(Boolean) });
      setMessage("Saved.");
      await refresh();
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred';
      setError(errorMessage);
    }
  };

  return (
    <RouteGuard>
      <div className="max-w-2xl mx-auto p-6">
        <h1 className="text-xl font-semibold mb-4">Your Profile</h1>
        <div className="mb-4">
          <ProfilePhotoUploader />
        </div>
        <form onSubmit={onSave} className="flex flex-col gap-3">
          <div className="flex flex-col">
            <label className="text-sm">Name</label>
            <input className="border p-2" value={name} onChange={(e) => setName(e.target.value)} />
          </div>
          <div className="flex flex-col">
            <label className="text-sm">Bio</label>
            <textarea className="border p-2" value={bio} onChange={(e) => setBio(e.target.value)} />
          </div>
          <div className="flex flex-col">
            <label className="text-sm">Skills (comma separated)</label>
            <input className="border p-2" value={skills} onChange={(e) => setSkills(e.target.value)} />
          </div>
          {message && <div className="text-green-700 text-sm">{message}</div>}
          {error && <div className="text-red-600 text-sm">{error}</div>}
          <button className="bg-black text-white p-2 rounded w-fit" type="submit">Save</button>
        </form>
        <div className="mt-6 text-sm text-gray-600">User ID: {cognitoUserId}</div>
      </div>
    </RouteGuard>
  );
}

