import { Amplify } from 'aws-amplify';
import { fetchAuthSession } from 'aws-amplify/auth';
import { Hub } from 'aws-amplify/utils';
import { generateClient } from 'aws-amplify/api';
import awsconfig from '../aws-exports';

// Configure Amplify with the generated config
const isLocalhost = typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || 
   window.location.hostname === '127.0.0.1' ||
   // For Docker containers
   window.location.hostname.endsWith('localhost'));

// Configure Amplify
const config = {
  ...awsconfig,
  Auth: {
    ...awsconfig.oauth,
    // Configure OAuth
    oauth: {
      ...awsconfig.oauth,
      redirectSignIn: isLocalhost ? 'http://localhost:3000/' : 'YOUR_PRODUCTION_URL',
      redirectSignOut: isLocalhost ? 'http://localhost:3000/login' : 'YOUR_PRODUCTION_URL/login',
      responseType: 'code',
    },
    // Configure cookie storage for SSR
    cookieStorage: {
      domain: isLocalhost ? 'localhost' : 'yourdomain.com',
      path: '/',
      expires: 30,
      sameSite: 'lax',
      secure: !isLocalhost,
    },
  },
  ssr: true,
};

// Initialize Amplify
Amplify.configure(config);

// Create API client with auth headers
const client = generateClient({
  authMode: 'userPool',
  authToken: (async () => {
    const session = await fetchAuthSession();
    return session.tokens?.idToken?.toString() || '';
  })() as unknown as string, // type override
});


// Helper function to ensure Amplify is configured
export function ensureAmplifyConfigured() {
  if (!Amplify.getConfig().Auth) {
    Amplify.configure(config);
  }
  return true;
}

// Export configured Amplify modules
export { client, Hub, fetchAuthSession };
