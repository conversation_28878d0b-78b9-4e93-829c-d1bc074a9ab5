"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import { AuthLayout } from "@/components/layouts";
import { Form, FormField, Input, Button, Loading } from "@/components/ui";

interface CognitoUserAttributes {
  'custom:role'?: string;
  email?: string;
  email_verified?: string;
  sub?: string;
  [key: string]: string | undefined;
}

interface CognitoUser {
  challengeName?: string;
  username?: string;
  attributes?: CognitoUserAttributes;
  signInUserSession?: {
    idToken?: {
      jwtToken?: string;
    };
  };
}

export default function LoginPage() {
  const { 
    signIn, 
    isAuthenticated, 
    user, 
    loading,
    completeNewPassword 
  } = useAuth();
  
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [showNewPasswordForm, setShowNewPasswordForm] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [challengeUser, setChallengeUser] = useState<CognitoUser | null>(null);

  useEffect(() => {
    if (isAuthenticated && user) {
      const searchParams = new URLSearchParams(window.location.search);
      const redirectTo = searchParams.get('redirect');
      
      if (redirectTo) {
        router.replace(redirectTo);
      } else {
        const userRole = user.attributes?.['custom:role'] as 'CLIENT' | 'FREELANCER' | 'ADMIN' | undefined;
        let redirectPath = '/';
        
        switch(userRole) {
          case 'CLIENT':
            redirectPath = '/client/dashboard';
            break;
          case 'FREELANCER':
            redirectPath = '/freelancer/dashboard';
            break;
          case 'ADMIN':
            redirectPath = '/admin/dashboard';
            break;
          default:
            redirectPath = '/';
        }
        
        router.replace(redirectPath);
      }
    }
  }, [isAuthenticated, user, router]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError(null);
    setIsSubmitting(true);
    
    try {
      const result = await signIn(email, password);
      
      if (!result) return;
      
      if (typeof result === 'object' && result !== null && 'challengeName' in result) {
        const cognitoUser = result as CognitoUser;
        if (cognitoUser.challengeName === 'NEW_PASSWORD_REQUIRED') {
          setChallengeUser(cognitoUser);
          setShowNewPasswordForm(true);
          setIsSubmitting(false);
          return;
        }
      }
    } catch (e) {
      const error = e as Error;
      setLoginError(error.message || "Login failed. Please check your credentials and try again.");
      setIsSubmitting(false);
    }
  };

  const handleNewPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!challengeUser) return;
    
    setLoginError(null);
    setIsSubmitting(true);
    
    try {
      await completeNewPassword(challengeUser, newPassword);
    } catch (e) {
      const error = e as Error;
      setLoginError(error.message || "Failed to set new password. Please try again.");
      setIsSubmitting(false);
    }
  };

  if (loading || (isAuthenticated && !isSubmitting)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (showNewPasswordForm) {
    return (
      <AuthLayout
        title="Set New Password"
        subtitle="Please set a new password for your account."
      >
        <Form onSubmit={handleNewPasswordSubmit}>
          <FormField
            label="New Password"
            required
            error={loginError || undefined}
            hint="Password must be at least 8 characters long"
          >
            <Input
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="Enter your new password"
              required
              minLength={8}
              error={!!loginError}
            />
          </FormField>

          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loading size="sm" className="mr-2" />
                Updating...
              </>
            ) : (
              'Update Password'
            )}
          </Button>
        </Form>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Welcome Back"
      subtitle="Sign in to your MyVillage account"
      className="w-full"
    >
      <Form onSubmit={handleLogin} className="w-full space-y-4">
        <div className="space-y-4">
          <FormField
            label="Email"
            required
            error={loginError && loginError.includes('email') ? loginError : undefined}
          >
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              error={!!loginError}
              className="w-full"
            />
          </FormField>

          <FormField
            label="Password"
            required
            error={loginError && !loginError.includes('email') ? loginError : undefined}
          >
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              minLength={8}
              error={!!loginError}
              className="w-full"
            />
          </FormField>
        </div>

        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
              Remember me
            </label>
          </div>
          <Link href="/forgot-password" className="text-sm font-medium text-primary hover:text-primary/80">
            Forgot password?
          </Link>
        </div>

        <div className="pt-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full py-2.5 text-base font-medium"
          >
            {isSubmitting ? (
              <>
                <Loading size="sm" className="mr-2" />
                Signing in...
              </>
            ) : (
              'Sign In'
            )}
          </Button>
        </div>

        <div className="text-center text-sm pt-2">
          <span className="text-muted-foreground">
            Don&apos;t have an account?{' '}
          </span>
          <Link href="/signup" className="font-medium text-primary hover:text-primary/80">
            Sign up
          </Link>
        </div>
      </Form>
    </AuthLayout>
  );
}
