'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { Job } from '@/types/job';
import { jobService } from '@/api/jobService';
import JobCard from '@/components/jobs/JobCard';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card';
import { Plus, Loader2 } from 'lucide-react';

const ClientJobsPage = () => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);

  useEffect(() => {
    if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'CLIENT')) {
      router.push('/login');
    }
  }, [authLoading, isAuthenticated, user, router]);

  const fetchJobs = useCallback(async () => {
    try {
      setIsLoading(true);
      if (!user?.username) {
        throw new Error('User not authenticated');
      }
      const data = await jobService.listMyJobs(user.username);
      setJobs(data);
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.username]);

  useEffect(() => {
    if (isAuthenticated && user?.attributes?.['custom:role'] === 'CLIENT') {
      fetchJobs();
    }
  }, [isAuthenticated, user, fetchJobs]);

  const handleDeleteJob = async (jobId: string) => {
    if (!window.confirm('Are you sure you want to delete this job posting? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleteLoading(jobId);
      await jobService.deleteJob(jobId);
      setJobs(jobs.filter(job => job.id !== jobId));
    } catch (error) {
      console.error('Error deleting job:', error);
    } finally {
      setDeleteLoading(null);
    }
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6">
      <Card className="shadow-sm">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle>My Job Postings</CardTitle>
              <CardDescription>Manage your job postings and applications</CardDescription>
            </div>
            <Button asChild>
              <Link href="/client/jobs/new">
                <Plus className="mr-2 h-4 w-4" />
                Post a New Job
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : jobs.length === 0 ? (
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-muted-foreground"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={1.5}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m9.016 14.25a48.11 48.11 0 01-9.113 0m9.113 0a2.18 2.18 0 01-1.5.533h-15a2.18 2.18 0 01-1.5-.533m15 0a2.18 2.18 0 00.75-1.661v-3.82a2.18 2.18 0 00-1.5-2.08M4.5 10.5h.01M9 15h.01m7.5-4.5v4.5m0-4.5v4.5"
                />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-foreground">No job postings yet</h3>
              <p className="mt-1 text-sm text-muted-foreground mb-6">
                You haven&apos;t posted any jobs yet. Get started by posting a new job.
              </p>
              <Button asChild>
                <Link href="/client/jobs/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Post a New Job
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Showing <span className="font-medium">{jobs.length}</span> {jobs.length === 1 ? 'job' : 'jobs'}
              </div>
              <div className="space-y-6">
                {jobs.map((job) => (
                  <div key={job.id} className="relative">
                    <JobCard 
                      job={job} 
                      showClientInfo={false}
                      showActions={true}
                      onEdit={() => router.push(`/client/jobs/${job.id}/edit`)}
                      onDelete={() => handleDeleteJob(job.id)}
                      deleteLoading={deleteLoading === job.id}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientJobsPage;
