'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/AuthContext';
import { Job, JobCategory, JobFilter } from '@/types/job';
import { jobService } from '@/api/jobService';
import JobCard from '@/components/jobs/JobCard';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Checkbox } from '@/components/ui/Checkbox';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Briefcase, Search, CheckSquare, Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';

type JobFilters = {
  searchTerm: string;
  category: JobCategory | '';
  minBudget: string;
  maxBudget: string;
  isRemote: boolean;
};

const JobsPage = () => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<JobFilters>({
    searchTerm: '',
    category: '',
    minBudget: '',
    maxBudget: '',
    isRemote: false,
  });

  useEffect(() => {
    if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'FREELANCER')) {
      router.push('/login');
    }
  }, [authLoading, isAuthenticated, user, router]);

  const fetchJobs = useCallback(async () => {
    if (!isAuthenticated || user?.attributes?.['custom:role'] !== 'FREELANCER') {
      return;
    }

    try {
      setIsLoading(true);
      const filterParams: JobFilter = {};
      
      if (filters.searchTerm) {
        filterParams.search = filters.searchTerm;
      }
      
      if (filters.category) {
        filterParams.category = filters.category;
      }
      
      if (filters.minBudget) {
        filterParams.minBudget = Number(filters.minBudget);
      }
      
      if (filters.maxBudget) {
        filterParams.maxBudget = Number(filters.maxBudget);
      }
      
      if (filters.isRemote) {
        filterParams.isRemote = true;
      }
      
      const { items } = await jobService.listJobs(filterParams);
      setJobs(items || []);
    } catch (error) {
      console.error('Error fetching jobs:', error);
      toast.error('Failed to load jobs');
    } finally {
      setIsLoading(false);
    }
  }, [filters, isAuthenticated, user?.attributes]);

  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    
    setFilters(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? e.target.checked : value
    }));
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilters(prev => ({
      ...prev,
      category: e.target.value as JobCategory
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      searchTerm: '',
      category: '',
      minBudget: '',
      maxBudget: '',
      isRemote: false,
    });
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const sidebarItems = [
    {
      name: 'Dashboard',
      href: '/freelancer/dashboard',
      icon: <Briefcase className="h-5 w-5" />,
    },
    {
      name: 'Find Jobs',
      href: '/freelancer/jobs',
      icon: <Search className="h-5 w-5" />,
    },
    {
      name: 'My Applications',
      href: '/freelancer/applications',
      icon: <CheckSquare className="h-5 w-5" />,
    },
  ];

  const breadcrumbs = [
    { label: 'Home', href: '/freelancer/dashboard' },
    { label: 'Find Jobs', href: '/freelancer/jobs' },
  ];

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      breadcrumbs={breadcrumbs}
      title="Find Jobs"
      description="Browse and apply to jobs that match your skills"
    >
      <Card className="shadow-sm">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle>Available Jobs</CardTitle>
              <CardDescription>Browse and apply to jobs that match your skills</CardDescription>
            </div>
            <Button asChild>
              <Link href="/freelancer/applications" className="flex items-center">
                <Briefcase className="mr-2 h-4 w-4" />
                My Applications
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>

          {/* Filters */}
          <div className="bg-muted/50 p-4 rounded-lg mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label htmlFor="searchTerm" className="block text-sm font-medium mb-1">
                  Search
                </label>
                <Input
                  type="text"
                  id="searchTerm"
                  name="searchTerm"
                  value={filters.searchTerm}
                  onChange={handleFilterChange}
                  placeholder="Job title or keywords"
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium mb-1">
                  Category
                </label>
                <Select
                  id="category"
                  name="category"
                  value={filters.category}
                  onChange={handleCategoryChange}
                  options={[
                    { value: '', label: 'All Categories' },
                    { value: 'WEB_DEVELOPMENT', label: 'Web Development' },
                    { value: 'MOBILE_DEVELOPMENT', label: 'Mobile Development' },
                    { value: 'DESIGN', label: 'Design' },
                    { value: 'WRITING', label: 'Writing' },
                    { value: 'MARKETING', label: 'Marketing' },
                    { value: 'BUSINESS', label: 'Business' },
                    { value: 'OTHER', label: 'Other' },
                  ]}
                  placeholder="Select a category"
                />
              </div>

              <div>
                <label htmlFor="minBudget" className="block text-sm font-medium mb-1">
                  Min Budget ($)
                </label>
                <Input
                  type="number"
                  id="minBudget"
                  name="minBudget"
                  min="0"
                  value={filters.minBudget}
                  onChange={handleFilterChange}
                  placeholder="Min"
                />
              </div>

              <div>
                <label htmlFor="maxBudget" className="block text-sm font-medium mb-1">
                  Max Budget ($)
                </label>
                <Input
                  type="number"
                  id="maxBudget"
                  name="maxBudget"
                  min={filters.minBudget || '0'}
                  value={filters.maxBudget}
                  onChange={handleFilterChange}
                  placeholder="Max"
                />
              </div>

              <div className="flex items-end">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="isRemote"
                    name="isRemote"
                    checked={filters.isRemote}
                    onChange={handleFilterChange}
                  />
                  <label
                    htmlFor="isRemote"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Remote Only
                  </label>
                </div>
              </div>

              <div className="flex justify-end md:col-span-2 lg:col-span-4">
                <Button 
                  variant="outline" 
                  onClick={handleClearFilters}
                  className="mt-2"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : jobs.length > 0 ? (
            <div className="space-y-4">
              {jobs.map((job) => (
                <JobCard key={job.id} job={job} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Briefcase className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-foreground">No jobs found</h3>
              <p className="text-muted-foreground mt-1">
                {Object.values(filters).some(Boolean)
                  ? 'Try adjusting your search or filter criteria'
                  : 'There are currently no jobs available. Please check back later.'}
              </p>
              {Object.values(filters).some(Boolean) && (
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={handleClearFilters}
                >
                  Clear all filters
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </DashboardLayout>
  );
}

export default JobsPage;
