'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/AuthContext';
import { JobApplication, Job } from '@/types/job';
import { jobService } from '@/api/jobService';
import { formatDistanceToNow } from 'date-fns';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card';
import { Briefcase, Search, Clock, CheckSquare, XCircle, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';

type JobApplicationWithJob = JobApplication & {
  job?: Job;
};

type ExtendedJobApplicationStatus = JobApplication['status'] | 'WITHDRAWN';

const MyApplicationsPage = () => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();

  const [applications, setApplications] = useState<JobApplicationWithJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  
  useEffect(() => {
    if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'FREELANCER')) {
      router.push('/login');
    }
  }, [authLoading, isAuthenticated, user, router]);

  const fetchApplications = useCallback(async () => {
    try {
      setIsLoading(true);
      if (!user?.username) {
        throw new Error('User not authenticated');
      }
      const data = await jobService.listMyApplications(user.username);
      setApplications(data);
    } catch (err) {
      console.error('Error fetching applications:', err);
      toast.error('Failed to load applications. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (isAuthenticated && user?.attributes?.['custom:role'] === 'FREELANCER') {
      fetchApplications();
    }
  }, [isAuthenticated, user, fetchApplications]);

  const filteredApplications = applications.filter(app => {
    const matchesSearch = !searchTerm || 
      app.job?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.job?.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'ALL' || app.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: ExtendedJobApplicationStatus) => {
    const baseClasses = 'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium';
    
    switch (status) {
      case 'PENDING':
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </span>
        );
      case 'ACCEPTED':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            <CheckCircle className="mr-1 h-3 w-3" />
            Accepted
          </span>
        );
      case 'REJECTED':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </span>
        );
      case 'WITHDRAWN':
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            <XCircle className="mr-1 h-3 w-3" />
            Withdrawn
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            <AlertCircle className="mr-1 h-3 w-3" />
            {status}
          </span>
        );
    }
  };

  const handleWithdrawApplication = async (applicationId: string) => {
    if (!window.confirm('Are you sure you want to withdraw this application? This action cannot be undone.')) {
      return;
    }

    try {
      setIsUpdating(true);
      await jobService.withdrawApplication(applicationId);
      
      setApplications(prev => prev.filter(app => app.id !== applicationId));
      
      toast.success('Application withdrawn successfully');
    } catch (err) {
      console.error('Error withdrawing application:', err);
      toast.error('Failed to withdraw application. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const sidebarItems = [
    {
      name: 'Dashboard',
      href: '/freelancer/dashboard',
      icon: <Briefcase className="h-5 w-5" />,
    },
    {
      name: 'Find Jobs',
      href: '/freelancer/jobs',
      icon: <Search className="h-5 w-5" />,
    },
    {
      name: 'My Applications',
      href: '/freelancer/applications',
      icon: <CheckSquare className="h-5 w-5" />,
    },
  ];

  const breadcrumbs = [
    { label: 'Home', href: '/freelancer/dashboard' },
    { label: 'My Applications', href: '/freelancer/applications' },
  ];

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      breadcrumbs={breadcrumbs}
      title="My Applications"
      description="Track the status of your job applications"
    >
      <div className="space-y-6">
        <Card className="shadow-sm">
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <CardTitle>My Applications</CardTitle>
                <CardDescription>Track the status of your job applications</CardDescription>
              </div>
              <Button asChild>
                <Link href="/freelancer/jobs" className="flex items-center">
                  <Search className="mr-2 h-4 w-4" />
                  Find More Jobs
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="bg-muted/50 p-4 rounded-lg mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="lg:col-span-2">
                  <label htmlFor="search" className="block text-sm font-medium mb-1">
                    Search
                  </label>
                  <Input
                    id="search"
                    placeholder="Search applications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label htmlFor="status-filter" className="block text-sm font-medium mb-1">
                    Status
                  </label>
                  <Select
                    id="status-filter"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    options={[
                      { value: 'ALL', label: 'All Statuses' },
                      { value: 'PENDING', label: 'Pending' },
                      { value: 'ACCEPTED', label: 'Accepted' },
                      { value: 'REJECTED', label: 'Rejected' },
                    ]}
                  />
                </div>
              </div>
            </div>

            {/* Applications List */}
            <div className="space-y-4">
              {isLoading ? (
                <div className="flex justify-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : filteredApplications.length > 0 ? (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {filteredApplications.map((application) => (
                <li key={application.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500 text-lg font-medium">
                            {application.job?.title?.charAt(0).toUpperCase() || 'J'}
                          </span>
                        </div>
                        <div className="ml-4">
                          <h3 className="text-lg font-medium text-gray-900">
                            {application.job?.title || 'Untitled Job'}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {application.createdAt 
                              ? `Applied ${formatDistanceToNow(new Date(application.createdAt), { addSuffix: true })}`
                              : 'Application date not available'}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        {getStatusBadge(application.status as ExtendedJobApplicationStatus)}
                        {application.proposedRate && (
                          <span className="mt-2 text-sm font-medium text-gray-900">
                            ${application.proposedRate.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Your Cover Letter</h4>
                      <p className="text-sm text-gray-700 line-clamp-2">
                        {application.coverLetter}
                      </p>
                    </div>
                    
                    <div className="mt-4 flex justify-between items-center">
                      <div>
                        <Link
                          href={`/jobs/${application.jobId}`}
                          className="text-sm font-medium text-blue-600 hover:text-blue-500"
                        >
                          View Job Details
                        </Link>
                      </div>
                      
                      <div className="flex space-x-2">
                        {application.status === 'PENDING' && (
                          <button
                            type="button"
                            onClick={() => handleWithdrawApplication(application.id)}
                            disabled={isUpdating}
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                          >
                            {isUpdating ? 'Withdrawing...' : 'Withdraw'}
                          </button>
                        )}
                        
                        {application.status === 'ACCEPTED' && (
                          <Link
                            href={`/messages/new?to=${application.job?.clientId}&jobId=${application.jobId}`}
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Message Client
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg p-6 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h3 className="mt-2 text-lg font-medium text-gray-900">
                {statusFilter === 'ALL' 
                  ? 'No applications yet' 
                  : `No ${statusFilter.toLowerCase()} applications`}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {statusFilter === 'ALL' 
                  ? "You haven't applied to any jobs yet. Start by browsing available jobs."
                  : `You don't have any ${statusFilter.toLowerCase()} applications.`}
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/freelancer/jobs" className="flex items-center">
                    <Search className="mr-2 h-4 w-4" />
                    Browse Jobs
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default MyApplicationsPage;
