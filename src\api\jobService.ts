import { Job, CreateJobInput, UpdateJobInput, JobFilter, JobApplication, CreateJobApplicationInput } from '@/types/job';
import { graphQLClient } from '@/lib/graphql/graphqlClient';
import { 
  CREATE_JOB, 
  UPDATE_JOB, 
  DELETE_JOB, 
  GET_JOB, 
  LIST_JOBS, 
  APPLY_FOR_JOB, 
  UPDATE_APPLICATION_STATUS, 
  WITHDRAW_APPLICATION 
} from '@/features/jobs/graphql';

// Define response types for GraphQL operations
interface CreateJobResponse {
  createJob: Job;
}

interface GetJobResponse {
  getJob: Job;
}

interface UpdateJobResponse {
  updateJob: Job;
}

interface DeleteJobResponse {
  deleteJob: {
    id: string;
  };
}

interface ListJobsResponse {
  // Response from GraphQL client with data wrapper
  data?: {
    listJobs: {
      items: Job[];
      nextToken?: string;
    };
  };
  // Direct response from GraphQL
  listJobs?: {
    items: Job[];
    nextToken?: string;
  };
  // Direct items array (fallback)
  items?: Job[];
  nextToken?: string;
}

// Removed unused ListMyJobsResponse interface

interface ApplyForJobResponse {
  applyForJob: JobApplication;
}

interface UpdateApplicationStatusResponse {
  updateApplicationStatus: JobApplication;
}

interface WithdrawApplicationResponse {
  withdrawApplication: {
    id: string;
  };
}

// Helper function to handle API errors
function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export const jobService = {
  // Job CRUD Operations
  async createJob(input: CreateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate<CreateJobResponse>(
        CREATE_JOB,
        { input }
      );
      return response.createJob;
    } catch (error) {
      return handleApiError('createJob', error);
    }
  },

  async getJob(id: string): Promise<Job> {
    try {
      const response = await graphQLClient.query<GetJobResponse>(
        GET_JOB,
        { id }
      );
      return response.getJob;
    } catch (error) {
      return handleApiError('getJob', error);
    }
  },

  async updateJob(input: UpdateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate<UpdateJobResponse>(
        UPDATE_JOB,
        { input }
      );
      return response.updateJob;
    } catch (error) {
      return handleApiError('updateJob', error);
    }
  },

  async deleteJob(id: string): Promise<void> {
    try {
      await graphQLClient.mutate<DeleteJobResponse>(
        DELETE_JOB,
        { input: { id } },
        { authMode: 'userPool' }
      );
    } catch (error) {
      handleApiError('deleteJob', error);
    }
  },

  // Job Listing & Filtering
  async listJobs(filter?: JobFilter): Promise<{ items: Job[]; nextToken?: string }> {
    try {
      // Define a type for the filter with proper typing for the budget field
      type TransformedFilter = Record<string, unknown> & {
        budget?: {
          ge?: number;
          le?: number;
        };
      };
      
      // Create a filter that matches the schema
      const transformedFilter: TransformedFilter = {};
      
      // Only add clientId if it exists
      if (filter?.clientId) {
        transformedFilter.clientId = { eq: filter.clientId };
      }
      
      // Only add category if it exists
      if (filter?.category) {
        transformedFilter.category = { eq: filter.category };
      }
      
      // Handle budget range if either min or max is provided
      if (filter?.minBudget !== undefined || filter?.maxBudget !== undefined) {
        transformedFilter.budget = {};
        if (filter.minBudget !== undefined) {
          transformedFilter.budget.ge = filter.minBudget;
        }
        if (filter.maxBudget !== undefined) {
          transformedFilter.budget.le = filter.maxBudget;
        }
      }

      const response = await graphQLClient.query<ListJobsResponse>(
        LIST_JOBS,
        { 
          filter: Object.keys(transformedFilter).length > 0 ? transformedFilter : undefined 
        }
      );
      
      
      if (response?.data?.listJobs) {
        return response.data.listJobs;
      } else if (response?.listJobs) {
        return response.listJobs;
      } else if (Array.isArray(response?.items)) {
        return { 
          items: response.items as Job[], 
          nextToken: response.nextToken as string | undefined 
        };
      }
      
      console.warn('Unexpected response format from listJobs:', response);
      return { items: [], nextToken: undefined };
    } catch (error) {
      console.error('Error in listJobs:', error);
      return handleApiError('listJobs', error);
    }
  },

  async listMyJobs(clientId: string): Promise<Job[]> {
    try {
      const result = await this.listJobs({ clientId });
      
      return result?.items || [];
    } catch (error) {
      console.error('Error in listMyJobs:', error);
      return handleApiError('listMyJobs', error);
    }
  },

  async applyForJob(input: CreateJobApplicationInput): Promise<JobApplication> {
    try {
      const response = await graphQLClient.mutate<ApplyForJobResponse>(
        APPLY_FOR_JOB,
        { input }
      );
      return response.applyForJob;
    } catch (error) {
      return handleApiError('applyForJob', error);
    }
  },

  async getJobApplications(jobId: string): Promise<JobApplication[]> {
    try {
      const job = await this.getJob(jobId);
      
      if (job && 'applications' in job && Array.isArray((job as { applications?: JobApplication[] }).applications)) {
        return (job as { applications: JobApplication[] }).applications;
      }
      
      return [];
    } catch (error) {
      return handleApiError('getJobApplications', error);
    }
  },

  async updateApplicationStatus(
    id: string, 
    status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'WITHDRAWN'
  ): Promise<JobApplication> {
    try {
      const response = await graphQLClient.mutate<UpdateApplicationStatusResponse>(
        UPDATE_APPLICATION_STATUS,
        { id, status }
      );
      return response.updateApplicationStatus;
    } catch (error) {
      return handleApiError('updateApplicationStatus', error);
    }
  },

  // Freelancer Applications
  async listMyApplications(freelancerId: string): Promise<JobApplication[]> {
    try {
      // TODO: Implement actual freelancer applications listing
      console.log('Fetching applications for freelancer:', freelancerId);
      return [];
    } catch (error) {
      return handleApiError('listMyApplications', error);
    }
  },

  async withdrawApplication(applicationId: string): Promise<void> {
    try {
      await graphQLClient.mutate<WithdrawApplicationResponse>(
        WITHDRAW_APPLICATION,
        { id: applicationId }
      );
    } catch (error) {
      handleApiError('withdrawApplication', error);
    }
  }
};
