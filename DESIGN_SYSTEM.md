# MyVillage Design System

A modern, minimal, and clean UI design system built with Tailwind CSS v4 and your custom earth-tone color palette.

## 🎨 Color Palette

Your design system uses a beautiful earth-tone color palette:

- **<PERSON>berwolf**: `#dad7cd` - Warm neutral base
- **Sage**: `#a3b18a` - Soft green accent
- **Fern Green**: `#588157` - Primary green
- **<PERSON> Green**: `#3a5a40` - Deep green
- **Brunswick Green**: `#344e41` - Darkest green

Each color has 9 shades (50-900) for maximum flexibility.

## 🚀 Quick Start

```tsx
import { Button, Card, CardContent } from '@/components/ui';
import { DashboardLayout } from '@/components/layouts';

function MyPage() {
  return (
    <DashboardLayout>
      <Card>
        <CardContent>
          <Button>Click me</Button>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
}
```

## 📁 Component Organization

```
src/components/
├── ui/                    # Core UI components
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Input.tsx
│   └── index.ts
├── layout/               # Layout components
│   ├── Container.tsx
│   ├── ModernNavbar.tsx
│   ├── Footer.tsx
│   └── index.ts
├── layouts/              # Page layout templates
│   ├── DashboardLayout.tsx
│   ├── MarketingLayout.tsx
│   ├── AppLayout.tsx
│   └── index.ts
├── examples/             # Usage examples
└── index.ts              # Main export file
```

## 🧩 Core Components

### Button
```tsx
<Button variant="default" size="lg">Primary</Button>
<Button variant="outline">Secondary</Button>
<Button variant="ghost">Ghost</Button>
```

### Card
```tsx
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>Content</CardContent>
  <CardFooter>Footer</CardFooter>
</Card>
```

### Form Components
```tsx
<Form onSubmit={handleSubmit}>
  <FormField label="Name" required error={errors.name}>
    <Input placeholder="Enter name" />
  </FormField>
  
  <FormField label="Bio">
    <Textarea placeholder="Tell us about yourself" />
  </FormField>
  
  <FormField label="Skills">
    <Select options={skillOptions} />
  </FormField>
</Form>
```

## 🏗️ Layout Templates

### Dashboard Layout
Perfect for admin panels and user dashboards:

```tsx
<DashboardLayout
  sidebarItems={sidebarItems}
  breadcrumbs={breadcrumbs}
  title="Dashboard"
  actions={<Button>Action</Button>}
>
  {/* Your content */}
</DashboardLayout>
```

### Marketing Layout
Great for landing pages and marketing sites:

```tsx
<MarketingLayout
  heroSection={
    <HeroSection
      title="Welcome to MyVillage"
      description="Connect with talented freelancers"
      actions={<Button>Get Started</Button>}
    />
  }
>
  <FeatureSection features={features} />
</MarketingLayout>
```

### App Layout
Simple layout for general application pages:

```tsx
<AppLayout>
  {/* Your app content */}
</AppLayout>
```

## 🌙 Dark Mode Support

All components support dark mode automatically using Tailwind's `dark:` variants:

```css
/* Automatically switches based on system preference */
.dark .bg-background { background: #0c120d; }
.dark .text-foreground { color: #edefe8; }
```

## 📱 Responsive Design

All components are built mobile-first with responsive breakpoints:

- `sm`: 640px and up
- `md`: 768px and up  
- `lg`: 1024px and up
- `xl`: 1280px and up

## ♿ Accessibility

Components include:
- Proper ARIA labels and roles
- Keyboard navigation support
- Focus management
- Screen reader compatibility
- Color contrast compliance

## 🎯 Design Tokens

### Spacing
- `xs`: 0.25rem (4px)
- `sm`: 0.5rem (8px)
- `md`: 1rem (16px)
- `lg`: 1.5rem (24px)
- `xl`: 2rem (32px)

### Typography
- Font family: Geist Sans (primary), Geist Mono (code)
- Font weights: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)

### Border Radius
- `sm`: 0.25rem
- `md`: 0.375rem
- `lg`: 0.5rem
- `xl`: 0.75rem

## 🔧 Customization

### Adding New Colors
Update `src/app/globals.css`:

```css
@theme inline {
  --color-custom-500: #your-color;
  --color-custom-600: #darker-shade;
}
```

### Creating New Components
Follow the established patterns:

```tsx
import { cn } from '@/lib/utils';

interface MyComponentProps {
  variant?: 'default' | 'custom';
  className?: string;
}

const MyComponent = ({ variant = 'default', className, ...props }) => {
  return (
    <div
      className={cn(
        'base-styles',
        variant === 'custom' && 'custom-styles',
        className
      )}
      {...props}
    />
  );
};
```

## 📚 Examples

Check out the example components in `src/components/examples/`:
- `DashboardExample.tsx` - Complete dashboard with stats and cards
- `MarketingExample.tsx` - Landing page with hero and features
- `FormExample.tsx` - Complex form with validation

## 🛠️ Development

### Prerequisites
- Next.js 15+
- Tailwind CSS v4
- TypeScript

### Installation
```bash
npm install clsx tailwind-merge
```

### Usage
Import components from the main index:

```tsx
import { Button, Card, DashboardLayout } from '@/components';
```

## 📄 License

This design system is part of the MyVillage project.
