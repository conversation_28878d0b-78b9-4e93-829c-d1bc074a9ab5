'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Loading } from '@/components/ui';

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Redirect to login if not authenticated and not loading
  useEffect(() => {
    if (!loading && !isAuthenticated && mounted) {
      router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
    } else if (!loading && isAuthenticated && user?.attributes?.['custom:role'] !== 'CLIENT') {
      // Redirect to appropriate dashboard based on user role
      const role = user?.attributes?.['custom:role']?.toLowerCase() || 'freelancer';
      router.push(`/${role}/dashboard`);
    }
  }, [isAuthenticated, loading, router, pathname, mounted, user]);

  // Show loading state while checking auth
  if (!mounted || loading || (!isAuthenticated && mounted) || user?.attributes?.['custom:role'] !== 'CLIENT') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const isActive = (href: string) => {
    if (href === '/client/dashboard') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const sidebarItems = [
    {
      name: 'Dashboard',
      href: '/client/dashboard',
      active: isActive('/client/dashboard'),
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
        </svg>
      ),
    },
    {
      name: 'My Jobs',
      href: '/client/jobs',
      active: isActive('/client/jobs') && !pathname.startsWith('/client/jobs/new'),
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 00-2 2H10a2 2 0 00-2-2V6m8 0h2a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h2" />
        </svg>
      ),
    },
    {
      name: 'Post a Job',
      href: '/client/jobs/new',
      active: pathname.startsWith('/client/jobs/new'),
      icon: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
    },
  ];

  // Get the current page title for the breadcrumb
  const getPageTitle = () => {
    if (pathname === '/client/dashboard') return 'Dashboard';
    if (pathname === '/client/jobs') return 'My Jobs';
    if (pathname === '/client/jobs/new') return 'Post a New Job';
    return '';
  };

  // Generate breadcrumbs based on the current path
  const breadcrumbs = [
    { label: 'Home', href: '/client/dashboard' },
    ...(pathname !== '/client/dashboard' ? [{ label: getPageTitle(), href: pathname }] : []),
  ];

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      breadcrumbs={breadcrumbs}
      title={getPageTitle()}
      className="bg-gray-50"
    >
      {children}
    </DashboardLayout>
  );
}
