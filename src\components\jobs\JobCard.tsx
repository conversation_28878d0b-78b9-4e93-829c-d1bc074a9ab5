import React from 'react';
import Link from 'next/link';
import { Job } from '@/types/job';
import { Loader2, Trash2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Badge } from '@/components/ui';
import { formatCurrency } from '@/lib/utils';

interface JobCardProps {
  job: Job;
  showClientInfo?: boolean;
  showActions?: boolean;
  onDelete?: (id: string) => void;
  onEdit?: (job: Job) => void;
  deleteLoading?: boolean;
}

export const JobCard: React.FC<JobCardProps> = ({
  job,
  showClientInfo = true,
  showActions = false,
  onDelete,
  onEdit,
  deleteLoading = false
}) => {
  const formattedDate = job.createdAt ? formatDistanceToNow(new Date(job.createdAt), { addSuffix: true }) : '';
  const deadlineDate = job.deadline ? new Date(job.deadline).toLocaleDateString() : 'No deadline';

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg">
              <Link href={`/jobs/${job.id}`} className="hover:text-primary transition-colors">
                {job.title}
              </Link>
            </CardTitle>

            {showClientInfo && formattedDate && (
              <CardDescription className="mt-1">
                Posted {formattedDate}
              </CardDescription>
            )}
          </div>

          {showActions && onEdit && onDelete && (
            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onEdit(job)}
                aria-label="Edit job"
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete(job.id)}
                className="text-destructive hover:bg-destructive/10"
                aria-label="Delete job"
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* Job metadata */}
          <div className="flex flex-wrap items-center gap-2 text-sm">
            {job.category && (
              <Badge variant="secondary">
                {job.category.replace('_', ' ')}
              </Badge>
            )}
            {job.budget && (
              <span className="text-muted-foreground">
                {formatCurrency(job.budget)}
              </span>
            )}
            <span className="text-muted-foreground">•</span>
            <span className="text-muted-foreground">
              Deadline: {deadlineDate}
            </span>
          </div>

          {/* Job description */}
          <p className="text-muted-foreground line-clamp-2">
            {job.description}
          </p>

          {/* Actions */}
          <div className="flex items-center justify-between pt-2">
            <Badge variant="outline">
              {job.category || 'Job'}
            </Badge>

            <Button variant="link" size="sm" asChild>
              <Link href={`/jobs/${job.id}`}>
                View Details →
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default JobCard;
