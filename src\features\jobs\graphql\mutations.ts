import { gql } from '@apollo/client';

export const CREATE_JOB = gql`
  mutation CreateJob($input: CreateJobInput!) {
    createJob(input: $input) {
      id
      title
      description
      budget
      category
      deadline
      clientId
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_JOB = gql`
  mutation UpdateJob($input: UpdateJobInput!) {
    updateJob(input: $input) {
      id
      title
      description
      budget
      category
      deadline
      updatedAt
    }
  }
`;

export const DELETE_JOB = gql`
  mutation DeleteJob($input: DeleteJobInput!) {
    deleteJob(input: $input) {
      id
    }
  }
`;

export const APPLY_FOR_JOB = gql`
  mutation ApplyForJob($input: CreateJobApplicationInput!) {
    applyForJob(input: $input) {
      id
      jobId
      freelancerId
      coverLetter
      proposedRate
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_APPLICATION_STATUS = gql`
  mutation UpdateApplicationStatus($id: ID!, $status: JobApplicationStatus!) {
    updateApplicationStatus(id: $id, status: $status) {
      id
      updatedAt
    }
  }
`;

export const WITHDRAW_APPLICATION = gql`
  mutation WithdrawApplication($id: ID!) {
    withdrawApplication(id: $id) {
      id
      updatedAt
    }
  }
`;
