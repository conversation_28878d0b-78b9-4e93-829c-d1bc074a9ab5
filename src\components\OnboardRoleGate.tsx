"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { generateClient } from "aws-amplify/api";
import * as queries from "@/lib/graphql/queries";
import * as mutations from "@/lib/graphql/mutations";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  profilePhoto?: string | null;
  bio?: string | null;
  skills?: string[] | null;
}

interface GetUserResponse {
  data?: {
    getUser?: User | null;
  };
}

const client = generateClient();

export default function OnboardRoleGate({ desiredRole, name }: { desiredRole: "CLIENT" | "FREELANCER"; name: string }) {
  const { user, updateProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    async function run() {
      const userId = user?.username;
      if (!userId) return;
      
      // Check if user already has a role set
      if (user.attributes?.['custom:role']) return;
      
      try {
        // Update the user's profile with the desired role
        await updateProfile({
          'custom:role': desiredRole
        });
        
        // Then create/update the user in the database
        const res = await client.graphql({ 
          query: queries.getUser, 
          variables: { id: userId } 
        });
        
        const existing = (res as GetUserResponse).data?.getUser;
        if (!existing) {
          await client.graphql({
            query: mutations.createUser,
            variables: { 
              input: { 
                id: userId, 
                name, 
                email: user.attributes?.email || "", 
                role: desiredRole 
              } 
            },
            authMode: "userPool",
          });
        }
        
        // Force a refresh of the page to update the UI
        router.refresh();
      } catch (e) {
        console.error('Error in OnboardRoleGate:', e);
      }
    }
    
    run();
  }, [user, desiredRole, name, router, updateProfile]);

  return null;
}

