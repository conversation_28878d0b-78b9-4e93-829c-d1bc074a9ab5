'use client';

import React from 'react';
import { ModernNavbar } from '@/components/layout/ModernNavbar';
import { Sidebar, SidebarItem } from '@/components/layout/Sidebar';
import { Container } from '@/components/layout/Container';
import { Breadcrumb, BreadcrumbItem } from '@/components/ui/Breadcrumb';
import { cn } from '@/lib/utils';

export interface DashboardLayoutProps {
  children: React.ReactNode;
  sidebarItems: SidebarItem[];
  breadcrumbs?: BreadcrumbItem[];
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  showSidebar?: boolean;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  sidebarItems,
  breadcrumbs,
  title,
  description,
  actions,
  className,
  showSidebar = true,
}) => {
  return (
    <div className="min-h-screen bg-background flex flex-col h-screen w-full">
      {/* Fixed Header */}
      <div className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <ModernNavbar />
      </div>
      
      <div className="flex flex-1 overflow-hidden w-full">
        {/* Fixed Sidebar */}
        {showSidebar && (
          <div className="hidden lg:flex flex-col w-64 border-r bg-background h-[calc(100vh-4rem)] sticky top-16 flex-shrink-0">
            <Sidebar items={sidebarItems} className="flex-1 overflow-y-auto" />
          </div>
        )}
        
        {/* Scrollable Content */}
        <main className={cn('flex-1 overflow-y-auto w-full', className)}>
          <div className="min-h-[calc(100vh-4rem)] w-full">
            <Container className="py-6 lg:py-8 px-4 sm:px-6">
              {/* Header */}
              {(breadcrumbs || title || actions) && (
                <div className="mb-8">
                  {breadcrumbs && (
                    <Breadcrumb items={breadcrumbs} className="mb-4" />
                  )}
                  
                  {(title || actions) && (
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        {title && (
                          <h1 className="text-3xl font-bold tracking-tight text-foreground">
                            {title}
                          </h1>
                        )}
                        {description && (
                          <p className="mt-2 text-muted-foreground">
                            {description}
                          </p>
                        )}
                      </div>
                      {actions && (
                        <div className="flex items-center gap-2">
                          {actions}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {/* Content */}
              <div className="space-y-6">
                {children}
              </div>
            </Container>
          </div>
        </main>
      </div>
    </div>
  );
};

export { DashboardLayout };
