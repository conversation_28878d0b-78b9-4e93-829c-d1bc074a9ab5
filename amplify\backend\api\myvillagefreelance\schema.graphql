
# Freelance Marketplace Amplify Schema

enum UserRole {
  CLIENT
  FREELANCER
}

enum JobStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

type User @model @auth(rules: [
  { allow: owner },
  { allow: groups, groups: ["admin"], operations: [read, update, delete] }
]) {
  id: ID!
  name: String!
  email: AWSEmail!
  role: UserRole!
  profilePhoto: String
  bio: String
  skills: [String]
}

type Job @model @auth(rules: [
  { allow: owner, ownerField: "clientId" },
  { allow: groups, groups: ["admin"], operations: [read, update, delete] },
  { allow: groups, groups: ["FREELANCER"], operations: [read] }
]) {
  id: ID!
  clientId: ID! # reference to User
  title: String!
  description: String!
  category: String!
  budget: Float!
  deadline: AWSDateTime
  isRemote: Boolean
  skills: [String]
  status: JobStatus @default(value: "OPEN")
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

enum ProposalStatus {
  PENDING
  ACCEPTED
  REJECTED
}

type Proposal @model @auth(rules: [
  { allow: owner, ownerField: "freelancerId" },
  { allow: groups, groups: ["admin"], operations: [read, update, delete] }
]) {
  id: ID!
  jobId: ID! # reference to Job
  freelancerId: ID! # reference to User
  bidAmount: Float!
  coverLetter: String
  status: ProposalStatus!
}

type Message @model @auth(rules: [
  { allow: owner, ownerField: "senderId" },
  { allow: owner, ownerField: "receiverId" },
  { allow: groups, groups: ["admin"], operations: [read, update, delete] }
]) {
  id: ID!
  conversationId: ID!
  senderId: ID! # reference to User
  receiverId: ID! # reference to User
  messageText: String!
  timestamp: AWSDateTime!
}

enum ContractStatus {
  IN_PROGRESS
  COMPLETED
}

type Contract @model @auth(rules: [
  { allow: owner, ownerField: "clientId" },
  { allow: owner, ownerField: "freelancerId" },
  { allow: groups, groups: ["admin"], operations: [read, update, delete] }
]) {
  id: ID!
  jobId: ID! # reference to Job
  clientId: ID! # reference to User
  freelancerId: ID! # reference to User
  status: ContractStatus!
  createdAt: AWSDateTime!
}

enum PaymentStatus {
  PAID
  PENDING
}

enum PaymentMethod {
  STRIPE
  USDC
  MYVILLAGETOKEN
}

type Payment @model @auth(rules: [
  { allow: owner },
  { allow: groups, groups: ["admin"], operations: [read, update, delete] }
]) {
  id: ID!
  contractId: ID! # reference to Contract
  amount: Float!
  status: PaymentStatus!
  method: PaymentMethod!
}
