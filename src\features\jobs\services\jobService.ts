import { graphQLClient } from '@/lib/graphql/graphqlClient';
import { GET_JOB, LIST_JOBS, LIST_MY_JOBS } from '../graphql/queries';
import {
  CREATE_JOB,
  UPDATE_JOB,
  DELETE_JOB,
  APPLY_FOR_JOB,
  UPDATE_APPLICATION_STATUS,
  WITHDRAW_APPLICATION,
} from '../graphql/mutations';
import { Job, CreateJobInput, UpdateJobInput, JobFilter, JobApplication, CreateJobApplicationInput, JobWithApplications } from '@/types/job';

// Helper function to handle API errors
function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export class JobService {
  // Get a single job by ID
  static async getJob(id: string): Promise<Job> {
    try {
      const response = await graphQLClient.query(
        GET_JOB,
        { id }
      ) as { getJob: Job };
      return response.getJob;
    } catch (error) {
      return handleApiError('getJob', error);
    }
  }

  // List jobs with optional filtering
  static async listJobs(filter?: JobFilter): Promise<{ items: Job[]; nextToken?: string }> {
    try {
      const response = await graphQLClient.query(
        LIST_JOBS,
        { filter }
      ) as { listJobs: { items: Job[]; nextToken?: string } };
      return response.listJobs;
    } catch (error) {
      return handleApiError('listJobs', error);
    }
  }

  // List jobs for the current client
  static async listMyJobs(clientId: string, status?: string): Promise<Job[]> {
    try {
      const response = await graphQLClient.query(
        LIST_MY_JOBS,
        { clientId, status }
      ) as { listMyJobs: { items: Job[] } };
      return response.listMyJobs.items;
    } catch (error) {
      return handleApiError('listMyJobs', error);
    }
  }

  // Create a new job
  static async createJob(input: CreateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate<{ createJob: Job }>(
        CREATE_JOB,
        { input },
        { authMode: 'userPool' }
      ) as { createJob: Job };
      
      return response.createJob;
    } catch (error) {
      console.error('Error in createJob:', error);
      return handleApiError('createJob', error);
    }
  }

  static async updateJob(input: UpdateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate(
        UPDATE_JOB,
        { input }
      ) as { updateJob: Job };
      return response.updateJob;
    } catch (error) {
      return handleApiError('updateJob', error);
    }
  }

  static async deleteJob(id: string): Promise<Job> {
    try {
      const response = await graphQLClient.mutate(
        DELETE_JOB,
        { input: { id } },
        { authMode: 'userPool' }
      ) as { deleteJob: Job };
      return response.deleteJob;
    } catch (error) {
      return handleApiError('deleteJob', error);
    }
  }

  static async applyForJob(input: CreateJobApplicationInput): Promise<JobApplication> {
    try {
      const response = await graphQLClient.mutate(
        APPLY_FOR_JOB,
        { input }
      ) as { applyForJob: JobApplication };
      return response.applyForJob;
    } catch (error) {
      return handleApiError('applyForJob', error);
    }
  }

  static async updateApplicationStatus(id: string, status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'WITHDRAWN'): Promise<JobApplication> {
    try {
      const response = await graphQLClient.mutate(
        UPDATE_APPLICATION_STATUS,
        { id, status }
      ) as { updateApplicationStatus: JobApplication };
      return response.updateApplicationStatus;
    } catch (error) {
      return handleApiError('updateApplicationStatus', error);
    }
  }

  static async withdrawApplication(id: string): Promise<JobApplication> {
    try {
      const response = await graphQLClient.mutate(
        WITHDRAW_APPLICATION,
        { id }
      ) as { withdrawApplication: JobApplication };
      return response.withdrawApplication;
    } catch (error) {
      return handleApiError('withdrawApplication', error);
    }
  }

  static async getJobApplications(jobId: string): Promise<JobApplication[]> {
    try {
      const job = await this.getJob(jobId) as JobWithApplications;
      
      if (job && 'applications' in job && Array.isArray(job.applications)) {
        return job.applications;
      }
      
      return [];
    } catch (error) {
      return handleApiError('getJobApplications', error);
    }
  }

  static async listMyApplications(): Promise<JobApplication[]> {
    // TODO: Implement job applications listing
    return [];
  }
}

export const jobService = new JobService();
export default jobService;
