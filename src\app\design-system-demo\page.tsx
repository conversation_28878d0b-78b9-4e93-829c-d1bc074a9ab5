'use client';

import React from 'react';
import { AppLayout } from '@/components/layouts';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Badge,
  Input,
  Textarea,
  Select,
  Checkbox,
  RadioGroup,
  Loading,
  Tabs,
  Pagination,
  Breadcrumb
} from '@/components/ui';

const DesignSystemDemo = () => {
  const tabItems = [
    {
      id: 'components',
      label: 'Components',
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Buttons</h3>
            <div className="flex flex-wrap gap-2">
              <Button>Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="link">Link</Button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Badges</h3>
            <div className="flex flex-wrap gap-2">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Loading States</h3>
            <div className="flex items-center gap-4">
              <Loading variant="spinner" size="sm" />
              <Loading variant="dots" size="md" />
              <Loading variant="pulse" size="lg" />
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'forms',
      label: 'Forms',
      content: (
        <div className="space-y-6 max-w-md">
          <div>
            <label className="block text-sm font-medium mb-2">Input</label>
            <Input placeholder="Enter text..." />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Textarea</label>
            <Textarea placeholder="Enter description..." rows={3} />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Select</label>
            <Select 
              options={[
                { value: '', label: 'Choose option...' },
                { value: 'option1', label: 'Option 1' },
                { value: 'option2', label: 'Option 2' },
              ]}
              placeholder="Choose option..."
            />
          </div>
          
          <div>
            <Checkbox label="Accept terms and conditions" />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Radio Group</label>
            <RadioGroup
              name="demo"
              options={[
                { value: 'option1', label: 'Option 1' },
                { value: 'option2', label: 'Option 2' },
              ]}
            />
          </div>
        </div>
      ),
    },
    {
      id: 'cards',
      label: 'Cards',
      content: (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Simple Card</CardTitle>
              <CardDescription>This is a basic card example</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Card content goes here. This demonstrates the basic card layout with header and content sections.</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Stats Card</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,234</div>
              <p className="text-xs text-muted-foreground">+20% from last month</p>
            </CardContent>
          </Card>
        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Design System', href: '/design-system-demo' },
    { label: 'Demo', current: true },
  ];

  return (
    <AppLayout>
      <div className="space-y-8">
        <div>
          <Breadcrumb items={breadcrumbItems} />
          <h1 className="text-3xl font-bold tracking-tight mt-4">Design System Demo</h1>
          <p className="text-muted-foreground mt-2">
            Explore the components and design tokens of the MyVillage design system.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Color Palette</CardTitle>
            <CardDescription>Our earth-tone color scheme</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="space-y-2">
                <div className="h-16 bg-timberwolf-500 rounded-lg"></div>
                <p className="text-sm font-medium">Timberwolf</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-sage-500 rounded-lg"></div>
                <p className="text-sm font-medium">Sage</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-fern-green-500 rounded-lg"></div>
                <p className="text-sm font-medium">Fern Green</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-hunter-green-500 rounded-lg"></div>
                <p className="text-sm font-medium">Hunter Green</p>
              </div>
              <div className="space-y-2">
                <div className="h-16 bg-brunswick-green-500 rounded-lg"></div>
                <p className="text-sm font-medium">Brunswick Green</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs items={tabItems} defaultTab="components" />

        <Card>
          <CardHeader>
            <CardTitle>Pagination Example</CardTitle>
          </CardHeader>
          <CardContent>
            <Pagination
              currentPage={3}
              totalPages={10}
              onPageChange={(page) => console.log('Page:', page)}
            />
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default DesignSystemDemo;
