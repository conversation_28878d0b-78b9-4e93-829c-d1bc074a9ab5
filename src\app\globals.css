@import "tailwindcss";

:root {
  /* Light mode colors */
  --background: #f8f7f5;
  --foreground: #344e41;
  --card: #ffffff;
  --card-foreground: #344e41;
  --popover: #ffffff;
  --popover-foreground: #344e41;
  --primary: #588157;
  --primary-foreground: #f8f7f5;
  --secondary: #dad7cd;
  --secondary-foreground: #344e41;
  --muted: #e9e7e1;
  --muted-foreground: #615b48;
  --accent: #a3b18a;
  --accent-foreground: #344e41;
  --destructive: #dc2626;
  --destructive-foreground: #f8f7f5;
  --border: #e2dfd7;
  --input: #e2dfd7;
  --ring: #588157;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Dark mode colors */
    --background: #0c120d;
    --foreground: #edefe8;
    --card: #172419;
    --card-foreground: #edefe8;
    --popover: #172419;
    --popover-foreground: #edefe8;
    --primary: #a3b18a;
    --primary-foreground: #0c120d;
    --secondary: #233323;
    --secondary-foreground: #edefe8;
    --muted: #233323;
    --muted-foreground: #92896c;
    --accent: #588157;
    --accent-foreground: #edefe8;
    --destructive: #dc2626;
    --destructive-foreground: #edefe8;
    --border: #233323;
    --input: #233323;
    --ring: #a3b18a;
  }
}

@theme inline {
  /* Custom color palette */
  --color-timberwolf-50: #f8f7f5;
  --color-timberwolf-100: #f0efeb;
  --color-timberwolf-200: #e9e7e1;
  --color-timberwolf-300: #e2dfd7;
  --color-timberwolf-400: #b6b09c;
  --color-timberwolf-500: #dad7cd;
  --color-timberwolf-600: #92896c;
  --color-timberwolf-700: #615b48;
  --color-timberwolf-800: #312e24;
  --color-timberwolf-900: #1a1815;

  --color-sage-50: #edefe8;
  --color-sage-100: #dae0d0;
  --color-sage-200: #c8d0b9;
  --color-sage-300: #b6c1a2;
  --color-sage-400: #859865;
  --color-sage-500: #a3b18a;
  --color-sage-600: #64724c;
  --color-sage-700: #434c33;
  --color-sage-800: #212619;
  --color-sage-900: #151a0f;

  --color-fern-green-50: #dce7dc;
  --color-fern-green-100: #b9cfb9;
  --color-fern-green-200: #96b795;
  --color-fern-green-300: #739f72;
  --color-fern-green-400: #466645;
  --color-fern-green-500: #588157;
  --color-fern-green-600: #344c34;
  --color-fern-green-700: #233323;
  --color-fern-green-800: #111911;
  --color-fern-green-900: #0a0f0a;

  --color-hunter-green-50: #d3e3d6;
  --color-hunter-green-100: #a7c7ac;
  --color-hunter-green-200: #7aaa83;
  --color-hunter-green-300: #56865f;
  --color-hunter-green-400: #2e4833;
  --color-hunter-green-500: #3a5a40;
  --color-hunter-green-600: #233626;
  --color-hunter-green-700: #172419;
  --color-hunter-green-800: #0c120d;
  --color-hunter-green-900: #060906;

  --color-brunswick-green-50: #d1e0d9;
  --color-brunswick-green-100: #a3c2b3;
  --color-brunswick-green-200: #75a38c;
  --color-brunswick-green-300: #527a66;
  --color-brunswick-green-400: #293d33;
  --color-brunswick-green-500: #344e41;
  --color-brunswick-green-600: #1f2e26;
  --color-brunswick-green-700: #141f1a;
  --color-brunswick-green-800: #0a0f0d;
  --color-brunswick-green-900: #050706;

  /* Design system colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Typography */
  --font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif;
  --font-mono: var(--font-geist-mono), ui-monospace, monospace;

  /* Spacing scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
