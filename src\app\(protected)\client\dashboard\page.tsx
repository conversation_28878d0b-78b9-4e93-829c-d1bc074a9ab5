"use client";
import React, { useEffect } from "react";
import { useAuth } from "@/lib/auth/AuthContext";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Loading } from "@/components/ui/Loading";
import { Briefcase, FileText, MessageSquare, Plus } from "lucide-react";

export default function ClientDashboard() {
  const { user, isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (
      !loading &&
      (!isAuthenticated || user?.attributes?.["custom:role"] !== "CLIENT")
    ) {
      router.push("/login");
    }
  }, [isAuthenticated, loading, user, router]);

  if (
    loading ||
    !isAuthenticated ||
    user?.attributes?.["custom:role"] !== "CLIENT"
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Active Jobs Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">+1 from last week</p>
          </CardContent>
        </Card>

        {/* Proposals Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Proposals Received
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">+3 from yesterday</p>
          </CardContent>
        </Card>

        {/* Messages Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Unread Messages
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">New messages</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Recent Jobs */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Jobs</CardTitle>
                <CardDescription>Your latest job postings</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/client/jobs")}
              >
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              {
                id: "1",
                title: "Website Development",
                status: "active",
                applications: 5,
                postedDate: "2023-06-15",
              },
              {
                id: "2",
                title: "Logo Design",
                status: "completed",
                applications: 12,
                postedDate: "2023-06-10",
              },
              {
                id: "3",
                title: "Content Writing",
                status: "active",
                applications: 3,
                postedDate: "2023-06-18",
              },
            ].map((job) => (
              <div
                key={job.id}
                className="flex items-center justify-between p-3 rounded-lg hover:bg-accent cursor-pointer transition-colors"
                onClick={() => router.push(`/client/jobs/${job.id}`)}
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{job.title}</p>
                  <div className="flex items-center text-xs text-muted-foreground mt-1">
                    <span>{job.applications} applications</span>
                    <span className="mx-2">•</span>
                    <span>Posted {job.postedDate}</span>
                  </div>
                </div>
                <Badge
                  variant={job.status === "completed" ? "success" : "default"}
                >
                  {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/client/jobs/new")}
            >
              <Plus className="mr-2 h-4 w-4" />
              Post a New Job
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/client/jobs")}
            >
              <Briefcase className="mr-2 h-4 w-4" />
              Manage Jobs
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/client/messages")}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              View Messages
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
