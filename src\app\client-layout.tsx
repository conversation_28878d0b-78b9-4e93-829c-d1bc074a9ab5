'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect } from 'react';
import Navbar from '@/components/Navbar';

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useAuth();
  const pathname = usePathname();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      let pageTitle = 'MyVillage Freelance';
      
      if (pathname.startsWith('/client')) {
        pageTitle = 'Client Dashboard | MyVillage';
      } else if (pathname.startsWith('/freelancer')) {
        pageTitle = 'Freelancer Dashboard | MyVillage';
      } else if (pathname.startsWith('/admin')) {
        pageTitle = 'Admin Dashboard | MyVillage';
      } else if (pathname === '/') {
        pageTitle = 'Welcome to MyVillage';
      }
      
      document.title = pageTitle;
    }
  }, [pathname, user]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="py-10">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {children}
        </div>
      </main>
    </div>
  );
}
